using System;
using UnityEngine;
using FairyGUI;

namespace MyGame.Filter
{
    /// <summary>
    /// 可控的自定义模糊滤镜，提供多种模糊算法和参数控制
    /// </summary>
    public class CustomBlurFilter : IFilter
    {
        [Range(0f, 10f)]
        public float blurSize = 1f;
        
        [Range(1, 8)]
        public int iterations = 2;
        
        [Range(1f, 8f)]
        public float downSample = 4f;
        
        public bool useGaussian = false;
        
        [Range(0f, 2f)]
        public float intensity = 1f;
        
        [Range(0f, 1f)]
        public float fadeEdges = 0f;
        
        public BlurQuality quality = BlurQuality.Medium;
        
        DisplayObject _target;
        Material _blitMaterial;
        
        public enum BlurQuality
        {
            Low = 4,
            Medium = 8,
            High = 16
        }
        
        public CustomBlurFilter()
        {
            blurSize = 1f;
            iterations = 2;
            downSample = 4f;
            useGaussian = false;
            intensity = 1f;
            fadeEdges = 0f;
            quality = BlurQuality.Medium;
        }
        
        public DisplayObject target
        {
            get { return _target; }
            set
            {
                _target = value;
                _target.EnterPaintingMode(1, null);
                _target.onPaint += OnRenderImage;
                
                _blitMaterial = new Material(ShaderConfig.GetShader("MyGame/CustomBlurFilter"));
                _blitMaterial.hideFlags = DisplayObject.hideFlags;
            }
        }
        
        public void Dispose()
        {
            if (_target != null)
            {
                _target.LeavePaintingMode(1);
                _target.onPaint -= OnRenderImage;
                _target = null;
            }
            
            if (_blitMaterial != null)
            {
                if (Application.isPlaying)
                    Material.Destroy(_blitMaterial);
                else
                    Material.DestroyImmediate(_blitMaterial);
                _blitMaterial = null;
            }
        }
        
        public void Update()
        {
        }
        
        /// <summary>
        /// 四点锥形模糊
        /// </summary>
        void FourTapCone(RenderTexture source, RenderTexture dest, int iteration)
        {
            float off = blurSize * iteration + 0.5f;
            _blitMaterial.SetFloat("_BlurIntensity", intensity);
            _blitMaterial.SetFloat("_FadeEdges", fadeEdges);
            Graphics.BlitMultiTap(source, dest, _blitMaterial,
                new Vector2(-off, -off),
                new Vector2(-off, off),
                new Vector2(off, off),
                new Vector2(off, -off)
            );
        }
        
        /// <summary>
        /// 高斯模糊
        /// </summary>
        void GaussianBlur(RenderTexture source, RenderTexture dest, int iteration)
        {
            float off = blurSize * (iteration + 1);
            _blitMaterial.SetFloat("_BlurIntensity", intensity);
            _blitMaterial.SetFloat("_BlurSize", off);
            _blitMaterial.SetFloat("_FadeEdges", fadeEdges);
            Graphics.Blit(source, dest, _blitMaterial, 1);
        }
        
        /// <summary>
        /// 降采样
        /// </summary>
        void DownSample(RenderTexture source, RenderTexture dest)
        {
            float off = 1f / downSample;
            Graphics.BlitMultiTap(source, dest, _blitMaterial,
                new Vector2(off, off),
                new Vector2(-off, off),
                new Vector2(off, -off),
                new Vector2(-off, -off)
            );
        }
        
        void OnRenderImage()
        {
            if (blurSize < 0.01f || intensity < 0.01f)
                return;

            RenderTexture sourceTexture = (RenderTexture)_target.paintingGraphics.texture.nativeTexture;
            int rtW = Mathf.RoundToInt(sourceTexture.width / downSample);
            int rtH = Mathf.RoundToInt(sourceTexture.height / downSample);

            rtW = Mathf.Max(rtW, (int)quality);
            rtH = Mathf.Max(rtH, (int)quality);

            RenderTexture buffer = RenderTexture.GetTemporary(rtW, rtH, 0);

            DownSample(sourceTexture, buffer);

            for (int i = 0; i < iterations; i++)
            {
                RenderTexture buffer2 = RenderTexture.GetTemporary(rtW, rtH, 0);

                if (useGaussian)
                    GaussianBlur(buffer, buffer2, i);
                else
                    FourTapCone(buffer, buffer2, i);

                RenderTexture.ReleaseTemporary(buffer);
                buffer = buffer2;
            }

            Graphics.Blit(buffer, sourceTexture);
            RenderTexture.ReleaseTemporary(buffer);
        }

        /// <summary>
        /// 设置模糊参数的便捷方法
        /// </summary>
        public void SetBlurParams(float size, int iter = -1, float intensity = -1f)
        {
            blurSize = size;
            if (iter >= 0) iterations = iter;
            if (intensity >= 0f) this.intensity = intensity;
        }

        /// <summary>
        /// 快速设置预设效果
        /// </summary>
        public void SetPreset(BlurPreset preset)
        {
            switch (preset)
            {
                case BlurPreset.Subtle:
                    blurSize = 0.5f;
                    iterations = 1;
                    intensity = 0.5f;
                    useGaussian = false;
                    break;
                case BlurPreset.Normal:
                    blurSize = 1f;
                    iterations = 2;
                    intensity = 1f;
                    useGaussian = false;
                    break;
                case BlurPreset.Strong:
                    blurSize = 2f;
                    iterations = 3;
                    intensity = 1.5f;
                    useGaussian = true;
                    break;
                case BlurPreset.Extreme:
                    blurSize = 4f;
                    iterations = 4;
                    intensity = 2f;
                    useGaussian = true;
                    break;
            }
        }

        public enum BlurPreset
        {
            Subtle,
            Normal,
            Strong,
            Extreme
        }
    }
}
