using System;
using UnityEngine;
using FairyGUI;

namespace MyGame.Filter
{
    /// <summary>
    /// 高级模糊滤镜，支持方向性模糊、动态效果等
    /// </summary>
    public class AdvancedBlurFilter : IFilter
    {
        [Range(0f, 10f)]
        public float blurSize = 1f;
        
        [Range(1, 8)]
        public int iterations = 2;
        
        [Range(0f, 2f)]
        public float intensity = 1f;
        
        public BlurDirection direction = BlurDirection.All;
        
        [Range(0f, 360f)]
        public float customAngle = 0f;
        
        public bool enableAnimation = false;
        
        [Range(0f, 5f)]
        public float animationSpeed = 1f;
        
        public AnimationType animationType = AnimationType.Pulse;
        
        [Range(0f, 1f)]
        public float noiseAmount = 0f;
        
        DisplayObject _target;
        Material _blitMaterial;
        float _animationTime = 0f;
        
        public enum BlurDirection
        {
            All,
            Horizontal,
            Vertical,
            Custom
        }
        
        public enum AnimationType
        {
            Pulse,
            Wave,
            Rotate,
            Noise
        }
        
        public AdvancedBlurFilter()
        {
            blurSize = 1f;
            iterations = 2;
            intensity = 1f;
            direction = BlurDirection.All;
            customAngle = 0f;
            enableAnimation = false;
            animationSpeed = 1f;
            animationType = AnimationType.Pulse;
            noiseAmount = 0f;
        }
        
        public DisplayObject target
        {
            get { return _target; }
            set
            {
                _target = value;
                _target.EnterPaintingMode(1, null);
                _target.onPaint += OnRenderImage;
                
                _blitMaterial = new Material(ShaderConfig.GetShader("MyGame/AdvancedBlurFilter"));
                _blitMaterial.hideFlags = DisplayObject.hideFlags;
            }
        }
        
        public void Dispose()
        {
            if (_target != null)
            {
                _target.LeavePaintingMode(1);
                _target.onPaint -= OnRenderImage;
                _target = null;
            }
            
            if (_blitMaterial != null)
            {
                if (Application.isPlaying)
                    Material.Destroy(_blitMaterial);
                else
                    Material.DestroyImmediate(_blitMaterial);
                _blitMaterial = null;
            }
        }
        
        public void Update()
        {
            if (enableAnimation)
            {
                _animationTime += Time.deltaTime * animationSpeed;
            }
        }
        
        Vector2 GetBlurDirection()
        {
            switch (direction)
            {
                case BlurDirection.Horizontal:
                    return new Vector2(1f, 0f);
                case BlurDirection.Vertical:
                    return new Vector2(0f, 1f);
                case BlurDirection.Custom:
                    float rad = customAngle * Mathf.Deg2Rad;
                    return new Vector2(Mathf.Cos(rad), Mathf.Sin(rad));
                default:
                    return Vector2.one;
            }
        }
        
        float GetAnimatedBlurSize()
        {
            if (!enableAnimation)
                return blurSize;
                
            switch (animationType)
            {
                case AnimationType.Pulse:
                    return blurSize * (1f + 0.5f * Mathf.Sin(_animationTime * 2f));
                case AnimationType.Wave:
                    return blurSize * (1f + 0.3f * Mathf.Sin(_animationTime * 3f) * Mathf.Cos(_animationTime * 1.5f));
                case AnimationType.Rotate:
                    customAngle = _animationTime * 60f % 360f;
                    return blurSize;
                case AnimationType.Noise:
                    return blurSize * (1f + noiseAmount * (Mathf.PerlinNoise(_animationTime, 0f) - 0.5f));
                default:
                    return blurSize;
            }
        }
        
        void DirectionalBlur(RenderTexture source, RenderTexture dest, int iteration)
        {
            Vector2 blurDir = GetBlurDirection();
            float currentBlurSize = GetAnimatedBlurSize();
            float off = currentBlurSize * iteration + 0.5f;
            
            _blitMaterial.SetFloat("_BlurIntensity", intensity);
            _blitMaterial.SetVector("_BlurDirection", blurDir);
            _blitMaterial.SetFloat("_NoiseAmount", noiseAmount);
            _blitMaterial.SetFloat("_Time", _animationTime);
            
            if (direction == BlurDirection.All)
            {
                Graphics.BlitMultiTap(source, dest, _blitMaterial,
                    new Vector2(-off, -off),
                    new Vector2(-off, off),
                    new Vector2(off, off),
                    new Vector2(off, -off)
                );
            }
            else
            {
                Vector2 offset = blurDir * off;
                Graphics.BlitMultiTap(source, dest, _blitMaterial,
                    -offset,
                    offset,
                    -offset * 0.5f,
                    offset * 0.5f
                );
            }
        }
        
        void OnRenderImage()
        {
            if (blurSize < 0.01f || intensity < 0.01f)
                return;
                
            RenderTexture sourceTexture = (RenderTexture)_target.paintingGraphics.texture.nativeTexture;
            int rtW = sourceTexture.width / 4;
            int rtH = sourceTexture.height / 4;
            RenderTexture buffer = RenderTexture.GetTemporary(rtW, rtH, 0);
            
            Graphics.Blit(sourceTexture, buffer);
            
            for (int i = 0; i < iterations; i++)
            {
                RenderTexture buffer2 = RenderTexture.GetTemporary(rtW, rtH, 0);
                DirectionalBlur(buffer, buffer2, i);
                RenderTexture.ReleaseTemporary(buffer);
                buffer = buffer2;
            }
            
            Graphics.Blit(buffer, sourceTexture);
            RenderTexture.ReleaseTemporary(buffer);
        }
        
        /// <summary>
        /// 设置方向性模糊
        /// </summary>
        public void SetDirectionalBlur(float angle, float size)
        {
            direction = BlurDirection.Custom;
            customAngle = angle;
            blurSize = size;
        }
        
        /// <summary>
        /// 启动动画效果
        /// </summary>
        public void StartAnimation(AnimationType type, float speed = 1f)
        {
            animationType = type;
            animationSpeed = speed;
            enableAnimation = true;
            _animationTime = 0f;
        }
        
        /// <summary>
        /// 停止动画效果
        /// </summary>
        public void StopAnimation()
        {
            enableAnimation = false;
        }
    }
}
