# 自定义模糊滤镜系统

基于FairyGUI的BlurFilter实现的更加可控的自定义模糊效果系统。

## 功能特性

### 1. CustomBlurFilter (基础自定义模糊)
- **可调节参数**：模糊大小、迭代次数、强度、降采样率
- **模糊算法**：支持四点锥形模糊和高斯模糊切换
- **质量控制**：Low/Medium/High三种质量等级
- **边缘淡化**：可选的边缘淡化效果
- **预设效果**：Subtle/Normal/Strong/Extreme四种预设

### 2. AdvancedBlurFilter (高级模糊)
- **方向性模糊**：支持水平、垂直、自定义角度方向
- **动画效果**：脉冲、波浪、旋转、噪声四种动画类型
- **噪声扰动**：可添加噪声增强视觉效果
- **实时控制**：支持运行时动态调整所有参数

### 3. BlurFilterManager (统一管理)
- **多目标支持**：可同时管理多个UI对象的模糊效果
- **类型切换**：在原生、自定义、高级模糊间无缝切换
- **批量控制**：统一控制多个对象的模糊参数
- **动画管理**：集中管理动画效果的启停

## 使用方法

### 基础使用
```csharp
// 创建自定义模糊滤镜
var blurFilter = new CustomBlurFilter();
blurFilter.blurSize = 2f;
blurFilter.intensity = 1.5f;
blurFilter.useGaussian = true;

// 应用到FairyGUI对象
gObject.displayObject.filter = blurFilter;
```

### 预设效果
```csharp
var blurFilter = new CustomBlurFilter();
blurFilter.SetPreset(CustomBlurFilter.BlurPreset.Strong);
```

### 方向性模糊
```csharp
var advancedFilter = new AdvancedBlurFilter();
advancedFilter.SetDirectionalBlur(45f, 3f); // 45度角，大小3
```

### 动画效果
```csharp
var advancedFilter = new AdvancedBlurFilter();
advancedFilter.StartAnimation(AdvancedBlurFilter.AnimationType.Pulse, 2f);
```

### 使用管理器
```csharp
// 在Inspector中设置目标对象名称
// 或通过代码设置
var manager = GetComponent<BlurFilterManager>();
manager.targetNames = new string[] { "BlurTarget1", "BlurTarget2" };
manager.SetBlurType(BlurFilterManager.BlurType.Advanced);
```

## 参数说明

### 基础参数
- **blurSize**: 模糊大小 (0-10)
- **intensity**: 模糊强度 (0-2)
- **iterations**: 迭代次数 (1-8)
- **downSample**: 降采样率 (1-8)

### 高级参数
- **direction**: 模糊方向 (All/Horizontal/Vertical/Custom)
- **customAngle**: 自定义角度 (0-360度)
- **animationType**: 动画类型 (Pulse/Wave/Rotate/Noise)
- **animationSpeed**: 动画速度 (0-5)
- **noiseAmount**: 噪声强度 (0-1)

### 质量设置
- **Low**: 4x4采样，性能最佳
- **Medium**: 8x8采样，平衡性能和质量
- **High**: 16x16采样，质量最佳

## 性能优化建议

1. **降采样**: 适当提高downSample值可显著提升性能
2. **迭代次数**: 减少iterations可降低GPU负载
3. **质量等级**: 根据设备性能选择合适的质量等级
4. **动画控制**: 不需要时及时停止动画效果
5. **批量管理**: 使用BlurFilterManager统一管理多个对象

## 注意事项

1. 模糊滤镜会创建RenderTexture，注意内存使用
2. 高迭代次数和高质量设置会增加GPU负载
3. 动画效果会持续消耗CPU资源
4. 记得在对象销毁时调用Dispose()释放资源
5. Shader文件需要放在Resources文件夹或可寻址资源中

## 示例场景

参考BlurFilterExample.cs了解完整的使用示例，包括：
- 实时参数调整
- 预设效果切换
- GUI控制面板
- 资源管理
