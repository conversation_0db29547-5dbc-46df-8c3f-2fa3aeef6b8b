// Shadow shader for FairyGUI based on FairyGUI/Image
// Optimized single-pass shadow rendering with blur and offset

Shader "FairyGUI/Shadow"
{
    Properties
    {
        _MainTex ("Base (RGB), Alpha (A)", 2D) = "black" {}
        
        // Shadow specific properties
        _ShadowOffset ("Shadow Offset", Vector) = (2, -2, 0, 0)
        _ShadowBlur ("Shadow Blur", Range(0, 10)) = 2.0
        _ShadowColor ("Shadow Color", Color) = (0, 0, 0, 0.5)
        _ShadowIntensity ("Shadow Intensity", Range(0, 2)) = 1.0

        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255

        _ColorMask ("Color Mask", Float) = 15

        _BlendSrcFactor ("Blend SrcFactor", Float) = 5
        _BlendDstFactor ("Blend DstFactor", Float) = 10
    }

    SubShader
    {
        LOD 100

        Tags
        {
            "Queue" = "Transparent"
            "IgnoreProjector" = "True"
            "RenderType" = "Transparent"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend [_BlendSrcFactor] [_BlendDstFactor]
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma target 2.0

            #pragma multi_compile __ GRAYED
            #pragma multi_compile __ CLIPPED
            #pragma multi_compile __ SOFT_CLIPPED
            #pragma multi_compile __ COLOR_FILTER
            #pragma multi_compile __ COMBINED

            #include "UnityCG.cginc"

            struct appdata_t
            {
                float4 vertex : POSITION;
                float4 color : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex : SV_POSITION;
                fixed4 color : COLOR;
                float4 texcoord : TEXCOORD0;

                #if defined(CLIPPED) || defined(SOFT_CLIPPED)
                float2 clipPos : TEXCOORD1;
                #endif
            };

            sampler2D _MainTex;
            float4 _MainTex_TexelSize;
            
            // Shadow properties
            float4 _ShadowOffset;
            float _ShadowBlur;
            float4 _ShadowColor;
            float _ShadowIntensity;
            
            #ifdef COMBINED
            sampler2D _AlphaTex;
            #endif

            CBUFFER_START(UnityPerMaterial)
            #ifdef CLIPPED
            float4 _ClipBox = float4(-2, -2, 0, 0);
            #endif

            #ifdef SOFT_CLIPPED
            float4 _ClipBox = float4(-2, -2, 0, 0);
            float4 _ClipSoftness = float4(0, 0, 0, 0);
            #endif
            CBUFFER_END

            #ifdef COLOR_FILTER
            float4x4 _ColorMatrix;
            float4 _ColorOffset;
            float _ColorOption = 0;
            #endif

            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord.xy = v.texcoord;
                o.texcoord.z = 1;
                o.texcoord.w = 1;

                #if !defined(UNITY_COLORSPACE_GAMMA) && (UNITY_VERSION >= 550)
                o.color.rgb = GammaToLinearSpace(v.color.rgb);
                o.color.a = v.color.a;
                #else
                o.color = v.color;
                #endif

                #ifdef CLIPPED
                o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                #endif

                #ifdef SOFT_CLIPPED
                o.clipPos = mul(unity_ObjectToWorld, v.vertex).xy * _ClipBox.zw + _ClipBox.xy;
                #endif

                return o;
            }
            
            // Simple blur sampling function
            fixed4 SampleBlur(sampler2D tex, float2 uv, float2 texelSize, float blurSize)
            {
                fixed4 color = fixed4(0, 0, 0, 0);
                float totalWeight = 0;
                
                // 9-tap blur sampling
                for (int x = -1; x <= 1; x++)
                {
                    for (int y = -1; y <= 1; y++)
                    {
                        float2 offset = float2(x, y) * texelSize * blurSize;
                        float weight = 1.0 / (1.0 + length(offset) * 2.0);
                        color += tex2D(tex, uv + offset) * weight;
                        totalWeight += weight;
                    }
                }
                
                return color / totalWeight;
            }

            fixed4 frag (v2f i) : SV_Target
            {

                // Calculate shadow UV with offset (compatible with FairyGUI UV handling)
                float2 shadowUV = i.texcoord.xy / i.texcoord.w + _ShadowOffset.xy * _MainTex_TexelSize.xy;
                
                // Sample shadow with blur
                fixed4 shadowSample;
                if (_ShadowBlur > 0.01)
                {
                    shadowSample = SampleBlur(_MainTex, shadowUV, _MainTex_TexelSize.xy, _ShadowBlur);
                }
                else
                {
                    shadowSample = tex2D(_MainTex, shadowUV);
                }
                
                // Create shadow color
                fixed4 col = _ShadowColor;
                col.a *= shadowSample.a * _ShadowIntensity * i.color.a;

                #ifdef COMBINED
                col.a *= tex2D(_AlphaTex, shadowUV).g;
                #endif

                #ifdef GRAYED
                fixed grey = dot(col.rgb, fixed3(0.299, 0.587, 0.114));
                col.rgb = fixed3(grey, grey, grey);
                #endif

                #ifdef SOFT_CLIPPED
                float2 factor = float2(0,0);
                if(i.clipPos.x<0)
                    factor.x = (1.0-abs(i.clipPos.x)) * _ClipSoftness.x;
                else
                    factor.x = (1.0-i.clipPos.x) * _ClipSoftness.z;
                if(i.clipPos.y<0)
                    factor.y = (1.0-abs(i.clipPos.y)) * _ClipSoftness.w;
                else
                    factor.y = (1.0-i.clipPos.y) * _ClipSoftness.y;
                col.a *= clamp(min(factor.x, factor.y), 0.0, 1.0);
                #endif

                #ifdef CLIPPED
                float2 factor = abs(i.clipPos);
                col.a *= step(max(factor.x, factor.y), 1);
                #endif

                #ifdef COLOR_FILTER
                if (_ColorOption == 0)
                {
                    col = mul(_ColorMatrix, col) + _ColorOffset;
                }
                else
                {
                    col.rgb = mul(_ColorMatrix, col.rgb) + _ColorOffset.rgb;
                }
                #endif

                return col;
            }
            ENDCG
        }
    }
}
