using UnityEngine;
using FairyGUI;
using MyGame.Filter;

namespace MyGame.Examples
{
    /// <summary>
    /// 自定义模糊滤镜使用示例
    /// </summary>
    public class BlurFilterExample : MonoBehaviour
    {
        [Header("目标对象")]
        public string targetObjectName = "BlurTarget";
        
        [Header("模糊参数")]
        [Range(0f, 10f)]
        public float blurSize = 1f;
        
        [Range(1, 8)]
        public int iterations = 2;
        
        [Range(1f, 8f)]
        public float downSample = 4f;
        
        public bool useGaussian = false;
        
        [Range(0f, 2f)]
        public float intensity = 1f;
        
        [Range(0f, 1f)]
        public float fadeEdges = 0f;
        
        public CustomBlurFilter.BlurQuality quality = CustomBlurFilter.BlurQuality.Medium;
        
        [Header("预设效果")]
        public CustomBlurFilter.BlurPreset preset = CustomBlurFilter.BlurPreset.Normal;
        
        [Header("控制")]
        public bool enableBlur = true;
        public bool usePreset = false;
        
        private CustomBlurFilter _blurFilter;
        private GObject _targetObject;
        
        void Start()
        {
            // 查找目标对象
            _targetObject = GRoot.inst.FindChild(targetObjectName);
            if (_targetObject == null)
            {
                Debug.LogWarning($"找不到目标对象: {targetObjectName}");
                return;
            }
            
            // 创建模糊滤镜
            _blurFilter = new CustomBlurFilter();
            ApplyBlurSettings();
            
            if (enableBlur)
            {
                _targetObject.displayObject.filter = _blurFilter;
            }
        }
        
        void Update()
        {
            if (_blurFilter == null || _targetObject == null)
                return;
                
            // 实时更新参数
            if (usePreset)
            {
                _blurFilter.SetPreset(preset);
            }
            else
            {
                ApplyBlurSettings();
            }
            
            // 控制模糊开关
            if (_targetObject.displayObject.filter != null && !enableBlur)
            {
                _targetObject.displayObject.filter = null;
            }
            else if (_targetObject.displayObject.filter == null && enableBlur)
            {
                _targetObject.displayObject.filter = _blurFilter;
            }
        }
        
        void ApplyBlurSettings()
        {
            if (_blurFilter == null) return;
            
            _blurFilter.blurSize = blurSize;
            _blurFilter.iterations = iterations;
            _blurFilter.downSample = downSample;
            _blurFilter.useGaussian = useGaussian;
            _blurFilter.intensity = intensity;
            _blurFilter.fadeEdges = fadeEdges;
            _blurFilter.quality = quality;
        }
        
        void OnDestroy()
        {
            if (_blurFilter != null)
            {
                _blurFilter.Dispose();
                _blurFilter = null;
            }
        }
        
        // GUI控制面板
        void OnGUI()
        {
            if (_targetObject == null) return;
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            GUILayout.Label("自定义模糊滤镜控制", GUI.skin.box);
            
            enableBlur = GUILayout.Toggle(enableBlur, "启用模糊");
            
            GUILayout.Space(10);
            usePreset = GUILayout.Toggle(usePreset, "使用预设");
            
            if (usePreset)
            {
                GUILayout.Label("预设效果:");
                if (GUILayout.Button("微妙")) preset = CustomBlurFilter.BlurPreset.Subtle;
                if (GUILayout.Button("普通")) preset = CustomBlurFilter.BlurPreset.Normal;
                if (GUILayout.Button("强烈")) preset = CustomBlurFilter.BlurPreset.Strong;
                if (GUILayout.Button("极端")) preset = CustomBlurFilter.BlurPreset.Extreme;
            }
            else
            {
                GUILayout.Label($"模糊大小: {blurSize:F1}");
                blurSize = GUILayout.HorizontalSlider(blurSize, 0f, 10f);
                
                GUILayout.Label($"迭代次数: {iterations}");
                iterations = (int)GUILayout.HorizontalSlider(iterations, 1, 8);
                
                GUILayout.Label($"强度: {intensity:F1}");
                intensity = GUILayout.HorizontalSlider(intensity, 0f, 2f);
                
                GUILayout.Label($"边缘淡化: {fadeEdges:F1}");
                fadeEdges = GUILayout.HorizontalSlider(fadeEdges, 0f, 1f);
                
                useGaussian = GUILayout.Toggle(useGaussian, "使用高斯模糊");
            }
            
            GUILayout.EndArea();
        }
    }
}
