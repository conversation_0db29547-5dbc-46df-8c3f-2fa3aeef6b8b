Shader "MyGame/CustomBlurFilter" {
    Properties {
        _MainTex ("Base (RGB)", 2D) = "white" {}
        _BlurIntensity ("Blur Intensity", Range(0, 2)) = 1
        _BlurSize ("Blur Size", Float) = 1
        _FadeEdges ("Fade Edges", Range(0, 1)) = 0
    }

    SubShader {
        // Pass 0: Four Tap Cone Blur
        Pass {
            ZTest Always 
            ZWrite Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                half2 texcoord : TEXCOORD0;
                half2 taps[4] : TEXCOORD1; 
            };

            sampler2D _MainTex;
            half4 _MainTex_TexelSize;
            half4 _BlurOffsets;
            half _BlurIntensity;
            half _FadeEdges;
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = v.texcoord - _BlurOffsets.xy * _MainTex_TexelSize.xy;
                o.taps[0] = o.texcoord + _MainTex_TexelSize * _BlurOffsets.xy;
                o.taps[1] = o.texcoord - _MainTex_TexelSize * _BlurOffsets.xy;
                o.taps[2] = o.texcoord + _MainTex_TexelSize * _BlurOffsets.xy * half2(1,-1);
                o.taps[3] = o.texcoord - _MainTex_TexelSize * _BlurOffsets.xy * half2(1,-1);
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                half4 color = tex2D(_MainTex, i.taps[0]);
                color += tex2D(_MainTex, i.taps[1]);
                color += tex2D(_MainTex, i.taps[2]);
                color += tex2D(_MainTex, i.taps[3]); 
                color *= 0.25;
                
                // 应用强度
                color.rgb = lerp(tex2D(_MainTex, i.texcoord).rgb, color.rgb, _BlurIntensity);
                
                // 边缘淡化
                if (_FadeEdges > 0) {
                    float2 center = abs(i.texcoord - 0.5) * 2;
                    float fade = 1 - saturate((max(center.x, center.y) - (1 - _FadeEdges)) / _FadeEdges);
                    color.a *= fade;
                }
                
                return color;
            }
            ENDCG
        }
        
        // Pass 1: Gaussian Blur
        Pass {
            ZTest Always 
            ZWrite Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                half2 texcoord : TEXCOORD0;
            };

            sampler2D _MainTex;
            half4 _MainTex_TexelSize;
            half _BlurSize;
            half _BlurIntensity;
            half _FadeEdges;
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = v.texcoord;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                half4 color = half4(0, 0, 0, 0);
                half2 blur = _MainTex_TexelSize.xy * _BlurSize;
                
                // 9点高斯采样
                color += tex2D(_MainTex, i.texcoord + half2(-blur.x, -blur.y)) * 0.0625;
                color += tex2D(_MainTex, i.texcoord + half2(0, -blur.y)) * 0.125;
                color += tex2D(_MainTex, i.texcoord + half2(blur.x, -blur.y)) * 0.0625;
                
                color += tex2D(_MainTex, i.texcoord + half2(-blur.x, 0)) * 0.125;
                color += tex2D(_MainTex, i.texcoord) * 0.25;
                color += tex2D(_MainTex, i.texcoord + half2(blur.x, 0)) * 0.125;
                
                color += tex2D(_MainTex, i.texcoord + half2(-blur.x, blur.y)) * 0.0625;
                color += tex2D(_MainTex, i.texcoord + half2(0, blur.y)) * 0.125;
                color += tex2D(_MainTex, i.texcoord + half2(blur.x, blur.y)) * 0.0625;
                
                // 应用强度
                color.rgb = lerp(tex2D(_MainTex, i.texcoord).rgb, color.rgb, _BlurIntensity);
                
                // 边缘淡化
                if (_FadeEdges > 0) {
                    float2 center = abs(i.texcoord - 0.5) * 2;
                    float fade = 1 - saturate((max(center.x, center.y) - (1 - _FadeEdges)) / _FadeEdges);
                    color.a *= fade;
                }
                
                return color;
            }
            ENDCG
        }
    }
    Fallback off
}
