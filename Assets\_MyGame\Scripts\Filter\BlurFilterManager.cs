using UnityEngine;
using FairyGUI;
using MyGame.Filter;

namespace MyGame.Filter
{
    /// <summary>
    /// 模糊滤镜管理器，统一管理各种模糊效果
    /// </summary>
    public class BlurFilterManager : MonoBehaviour
    {
        [Header("目标设置")]
        public string[] targetNames;
        
        [Header("模糊类型")]
        public BlurType blurType = BlurType.Custom;
        
        [Header("基础参数")]
        [Range(0f, 10f)]
        public float blurSize = 1f;
        
        [Range(0f, 2f)]
        public float intensity = 1f;
        
        [Range(1, 8)]
        public int iterations = 2;
        
        [Header("高级参数")]
        public AdvancedBlurFilter.BlurDirection direction = AdvancedBlurFilter.BlurDirection.All;
        
        [Range(0f, 360f)]
        public float customAngle = 0f;
        
        [Header("动画设置")]
        public bool enableAnimation = false;
        public AdvancedBlurFilter.AnimationType animationType = AdvancedBlurFilter.AnimationType.Pulse;
        
        [Range(0f, 5f)]
        public float animationSpeed = 1f;
        
        [Header("控制")]
        public bool enableBlur = true;
        
        private GObject[] _targetObjects;
        private IFilter[] _currentFilters;
        
        public enum BlurType
        {
            Original,    // FairyGUI原生BlurFilter
            Custom,      // 自定义模糊
            Advanced     // 高级模糊
        }
        
        void Start()
        {
            InitializeTargets();
            ApplyBlurFilters();
        }
        
        void Update()
        {
            UpdateBlurFilters();
        }
        
        void InitializeTargets()
        {
            if (targetNames == null || targetNames.Length == 0)
                return;
                
            _targetObjects = new GObject[targetNames.Length];
            _currentFilters = new IFilter[targetNames.Length];
            
            for (int i = 0; i < targetNames.Length; i++)
            {
                _targetObjects[i] = GRoot.inst.FindChild(targetNames[i]);
                if (_targetObjects[i] == null)
                {
                    Debug.LogWarning($"找不到目标对象: {targetNames[i]}");
                }
            }
        }
        
        void ApplyBlurFilters()
        {
            if (_targetObjects == null) return;
            
            for (int i = 0; i < _targetObjects.Length; i++)
            {
                if (_targetObjects[i] == null) continue;
                
                // 清理旧的滤镜
                if (_currentFilters[i] != null)
                {
                    _currentFilters[i].Dispose();
                    _currentFilters[i] = null;
                }
                
                if (!enableBlur)
                {
                    _targetObjects[i].displayObject.filter = null;
                    continue;
                }
                
                // 创建新的滤镜
                IFilter newFilter = CreateBlurFilter();
                if (newFilter != null)
                {
                    _currentFilters[i] = newFilter;
                    _targetObjects[i].displayObject.filter = newFilter;
                }
            }
        }
        
        IFilter CreateBlurFilter()
        {
            switch (blurType)
            {
                case BlurType.Original:
                    var originalFilter = new BlurFilter();
                    originalFilter.blurSize = blurSize;
                    return originalFilter;
                    
                case BlurType.Custom:
                    var customFilter = new CustomBlurFilter();
                    customFilter.blurSize = blurSize;
                    customFilter.intensity = intensity;
                    customFilter.iterations = iterations;
                    return customFilter;
                    
                case BlurType.Advanced:
                    var advancedFilter = new AdvancedBlurFilter();
                    advancedFilter.blurSize = blurSize;
                    advancedFilter.intensity = intensity;
                    advancedFilter.iterations = iterations;
                    advancedFilter.direction = direction;
                    advancedFilter.customAngle = customAngle;
                    advancedFilter.enableAnimation = enableAnimation;
                    advancedFilter.animationType = animationType;
                    advancedFilter.animationSpeed = animationSpeed;
                    return advancedFilter;
                    
                default:
                    return null;
            }
        }
        
        void UpdateBlurFilters()
        {
            if (_currentFilters == null) return;
            
            for (int i = 0; i < _currentFilters.Length; i++)
            {
                if (_currentFilters[i] == null) continue;
                
                // 更新参数
                switch (blurType)
                {
                    case BlurType.Original:
                        if (_currentFilters[i] is BlurFilter originalFilter)
                        {
                            originalFilter.blurSize = blurSize;
                        }
                        break;
                        
                    case BlurType.Custom:
                        if (_currentFilters[i] is CustomBlurFilter customFilter)
                        {
                            customFilter.blurSize = blurSize;
                            customFilter.intensity = intensity;
                            customFilter.iterations = iterations;
                        }
                        break;
                        
                    case BlurType.Advanced:
                        if (_currentFilters[i] is AdvancedBlurFilter advancedFilter)
                        {
                            advancedFilter.blurSize = blurSize;
                            advancedFilter.intensity = intensity;
                            advancedFilter.iterations = iterations;
                            advancedFilter.direction = direction;
                            advancedFilter.customAngle = customAngle;
                            advancedFilter.enableAnimation = enableAnimation;
                            advancedFilter.animationType = animationType;
                            advancedFilter.animationSpeed = animationSpeed;
                        }
                        break;
                }
            }
        }
        
        void OnDestroy()
        {
            if (_currentFilters != null)
            {
                for (int i = 0; i < _currentFilters.Length; i++)
                {
                    if (_currentFilters[i] != null)
                    {
                        _currentFilters[i].Dispose();
                        _currentFilters[i] = null;
                    }
                }
            }
        }
        
        // 公共接口
        public void SetBlurType(BlurType type)
        {
            if (blurType != type)
            {
                blurType = type;
                ApplyBlurFilters();
            }
        }
        
        public void SetBlurSize(float size)
        {
            blurSize = size;
        }
        
        public void SetIntensity(float value)
        {
            intensity = value;
        }
        
        public void ToggleBlur()
        {
            enableBlur = !enableBlur;
            ApplyBlurFilters();
        }
        
        public void StartAnimation(AdvancedBlurFilter.AnimationType type, float speed = 1f)
        {
            if (blurType == BlurType.Advanced)
            {
                animationType = type;
                animationSpeed = speed;
                enableAnimation = true;
            }
        }
        
        public void StopAnimation()
        {
            enableAnimation = false;
        }
    }
}
