Shader "MyGame/AdvancedBlurFilter" {
    Properties {
        _MainTex ("Base (RGB)", 2D) = "white" {}
        _BlurIntensity ("Blur Intensity", Range(0, 2)) = 1
        _BlurDirection ("Blur Direction", Vector) = (1, 1, 0, 0)
        _NoiseAmount ("Noise Amount", Range(0, 1)) = 0
        _Time ("Time", Float) = 0
    }

    SubShader {
        Pass {
            ZTest Always 
            ZWrite Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                half2 texcoord : TEXCOORD0;
                half2 taps[4] : TEXCOORD1; 
            };

            sampler2D _MainTex;
            half4 _MainTex_TexelSize;
            half4 _BlurOffsets;
            half _BlurIntensity;
            half4 _BlurDirection;
            half _NoiseAmount;
            half _Time;
            
            // 简单噪声函数
            float noise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = v.texcoord;
                
                // 计算采样点偏移
                half2 blurOffset = _BlurOffsets.xy * _MainTex_TexelSize.xy;
                
                // 如果是方向性模糊，调整偏移方向
                if (length(_BlurDirection.xy) > 0.1) {
                    blurOffset *= normalize(_BlurDirection.xy);
                }
                
                // 添加噪声扰动
                if (_NoiseAmount > 0) {
                    float2 noiseOffset = (noise(v.texcoord + _Time) - 0.5) * _NoiseAmount * _MainTex_TexelSize.xy;
                    blurOffset += noiseOffset;
                }
                
                o.taps[0] = o.texcoord + blurOffset;
                o.taps[1] = o.texcoord - blurOffset;
                o.taps[2] = o.texcoord + blurOffset * half2(1, -1);
                o.taps[3] = o.texcoord - blurOffset * half2(1, -1);
                
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                half4 color = half4(0, 0, 0, 0);
                
                // 基础采样
                half4 center = tex2D(_MainTex, i.texcoord);
                
                // 多点采样
                color += tex2D(_MainTex, i.taps[0]) * 0.25;
                color += tex2D(_MainTex, i.taps[1]) * 0.25;
                color += tex2D(_MainTex, i.taps[2]) * 0.25;
                color += tex2D(_MainTex, i.taps[3]) * 0.25;
                
                // 混合原始颜色和模糊颜色
                color.rgb = lerp(center.rgb, color.rgb, _BlurIntensity);
                color.a = center.a;
                
                // 添加噪声效果
                if (_NoiseAmount > 0) {
                    float noiseValue = noise(i.texcoord * 100 + _Time);
                    color.rgb = lerp(color.rgb, color.rgb * noiseValue, _NoiseAmount * 0.1);
                }
                
                return color;
            }
            ENDCG
        }
        
        // Pass 1: 高质量方向性模糊
        Pass {
            ZTest Always 
            ZWrite Off

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include "UnityCG.cginc"

            struct appdata_t {
                float4 vertex : POSITION;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f {
                float4 vertex : SV_POSITION;
                half2 texcoord : TEXCOORD0;
            };

            sampler2D _MainTex;
            half4 _MainTex_TexelSize;
            half _BlurIntensity;
            half4 _BlurDirection;
            half _NoiseAmount;
            half _Time;
            
            float noise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }
            
            v2f vert (appdata_t v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.texcoord = v.texcoord;
                return o;
            }
            
            fixed4 frag (v2f i) : SV_Target
            {
                half4 color = half4(0, 0, 0, 0);
                half2 blurDir = normalize(_BlurDirection.xy);
                half2 texelSize = _MainTex_TexelSize.xy;
                
                // 7点采样
                for (int j = -3; j <= 3; j++) {
                    half2 offset = blurDir * texelSize * j * 2;
                    
                    // 添加噪声扰动
                    if (_NoiseAmount > 0) {
                        float2 noiseOffset = (noise(i.texcoord + offset + _Time) - 0.5) * _NoiseAmount * texelSize;
                        offset += noiseOffset;
                    }
                    
                    half weight = exp(-abs(j) * 0.5); // 高斯权重
                    color += tex2D(_MainTex, i.texcoord + offset) * weight;
                }
                
                color /= 7; // 归一化
                
                // 混合原始颜色
                half4 original = tex2D(_MainTex, i.texcoord);
                color.rgb = lerp(original.rgb, color.rgb, _BlurIntensity);
                color.a = original.a;
                
                return color;
            }
            ENDCG
        }
    }
    Fallback off
}
