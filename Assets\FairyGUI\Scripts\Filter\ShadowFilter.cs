using System;
using UnityEngine;

namespace FairyGUI
{
    /// <summary>
    /// 高性能阴影滤镜，使用专用shader进行单次渲染
    /// </summary>
    public class ShadowFilter : IFilter
    {
        /// <summary>
        /// 阴影偏移
        /// </summary>
        public Vector2 shadowOffset;

        /// <summary>
        /// 阴影模糊强度
        /// </summary>
        public float shadowBlur;

        /// <summary>
        /// 阴影颜色
        /// </summary>
        public Color shadowColor;

        /// <summary>
        /// 阴影强度
        /// </summary>
        public float shadowIntensity;

        DisplayObject _target;
        Material _shadowMaterial;

        public ShadowFilter()
        {
            shadowOffset = new Vector2(2f, -2f);
            shadowBlur = 2f;
            shadowColor = new Color(0f, 0f, 0f, 0.5f);
            shadowIntensity = 1f;
        }

        public ShadowFilter(Vector2 offset, float blur, Color color, float intensity)
        {
            shadowOffset = offset;
            shadowBlur = blur;
            shadowColor = color;
            shadowIntensity = intensity;
        }

        public DisplayObject target
        {
            get { return _target; }
            set
            {
                _target = value;
                
                // 获取shadow shader
                Shader shadowShader = ShaderConfig.GetShader("FairyGUI/Shadow");
                if (shadowShader == null)
                {
                    Debug.LogError("ShadowFilter: Cannot find FairyGUI/Shadow shader!");
                    return;
                }

                // 创建shadow material
                _shadowMaterial = new Material(shadowShader);
                _shadowMaterial.hideFlags = DisplayObject.hideFlags;
                
                // 设置初始参数
                UpdateMaterialProperties();
                
                // 设置目标对象使用shadow material
                if (_target.graphics != null && _target.graphics.material != null)
                {
                    // 保存原始material的一些属性
                    var originalMaterial = _target.graphics.material;
                    
                    // 复制stencil和blend设置
                    if (originalMaterial.HasProperty("_StencilComp"))
                        _shadowMaterial.SetFloat("_StencilComp", originalMaterial.GetFloat("_StencilComp"));
                    if (originalMaterial.HasProperty("_Stencil"))
                        _shadowMaterial.SetFloat("_Stencil", originalMaterial.GetFloat("_Stencil"));
                    if (originalMaterial.HasProperty("_StencilOp"))
                        _shadowMaterial.SetFloat("_StencilOp", originalMaterial.GetFloat("_StencilOp"));
                    if (originalMaterial.HasProperty("_StencilWriteMask"))
                        _shadowMaterial.SetFloat("_StencilWriteMask", originalMaterial.GetFloat("_StencilWriteMask"));
                    if (originalMaterial.HasProperty("_StencilReadMask"))
                        _shadowMaterial.SetFloat("_StencilReadMask", originalMaterial.GetFloat("_StencilReadMask"));
                    if (originalMaterial.HasProperty("_BlendSrcFactor"))
                        _shadowMaterial.SetFloat("_BlendSrcFactor", originalMaterial.GetFloat("_BlendSrcFactor"));
                    if (originalMaterial.HasProperty("_BlendDstFactor"))
                        _shadowMaterial.SetFloat("_BlendDstFactor", originalMaterial.GetFloat("_BlendDstFactor"));
                    if (originalMaterial.HasProperty("_ColorMask"))
                        _shadowMaterial.SetFloat("_ColorMask", originalMaterial.GetFloat("_ColorMask"));
                }
                
                // 应用shadow material
                _target.graphics.material = _shadowMaterial;
            }
        }

        public void Dispose()
        {
            if (_target != null)
            {
                // 恢复原始material（通常是默认的Image material）
                _target.graphics.material = null;
                _target = null;
            }

            if (_shadowMaterial != null)
            {
                if (Application.isPlaying)
                    Material.Destroy(_shadowMaterial);
                else
                    Material.DestroyImmediate(_shadowMaterial);
                _shadowMaterial = null;
            }
        }

        public void Update()
        {
            if (_shadowMaterial != null)
            {
                UpdateMaterialProperties();
            }
        }

        private void UpdateMaterialProperties()
        {
            if (_shadowMaterial == null) return;

            _shadowMaterial.SetVector("_ShadowOffset", new Vector4(shadowOffset.x, shadowOffset.y, 0, 0));
            _shadowMaterial.SetFloat("_ShadowBlur", shadowBlur);
            _shadowMaterial.SetColor("_ShadowColor", shadowColor);
            _shadowMaterial.SetFloat("_ShadowIntensity", shadowIntensity);
        }

        /// <summary>
        /// 设置阴影参数
        /// </summary>
        public void SetShadowParams(Vector2 offset, float blur, Color color, float intensity)
        {
            shadowOffset = offset;
            shadowBlur = blur;
            shadowColor = color;
            shadowIntensity = intensity;
            UpdateMaterialProperties();
        }

        /// <summary>
        /// 设置阴影偏移
        /// </summary>
        public void SetShadowOffset(Vector2 offset)
        {
            shadowOffset = offset;
            if (_shadowMaterial != null)
                _shadowMaterial.SetVector("_ShadowOffset", new Vector4(offset.x, offset.y, 0, 0));
        }

        /// <summary>
        /// 设置阴影模糊
        /// </summary>
        public void SetShadowBlur(float blur)
        {
            shadowBlur = blur;
            if (_shadowMaterial != null)
                _shadowMaterial.SetFloat("_ShadowBlur", blur);
        }

        /// <summary>
        /// 设置阴影颜色
        /// </summary>
        public void SetShadowColor(Color color)
        {
            shadowColor = color;
            if (_shadowMaterial != null)
                _shadowMaterial.SetColor("_ShadowColor", color);
        }

        /// <summary>
        /// 设置阴影强度
        /// </summary>
        public void SetShadowIntensity(float intensity)
        {
            shadowIntensity = intensity;
            if (_shadowMaterial != null)
                _shadowMaterial.SetFloat("_ShadowIntensity", intensity);
        }

        /// <summary>
        /// 阴影预设类型
        /// </summary>
        public enum ShadowPreset
        {
            Subtle,     // 微妙阴影
            Normal,     // 普通阴影
            Strong,     // 强阴影
            Dramatic,   // 戏剧性阴影
            Custom      // 自定义
        }

        /// <summary>
        /// 应用阴影预设
        /// </summary>
        public void ApplyPreset(ShadowPreset preset)
        {
            switch (preset)
            {
                case ShadowPreset.Subtle:
                    SetShadowParams(new Vector2(1f, -1f), 0.5f, new Color(0f, 0f, 0f, 0.3f), 0.8f);
                    break;
                case ShadowPreset.Normal:
                    SetShadowParams(new Vector2(2f, -2f), 1.5f, new Color(0f, 0f, 0f, 0.5f), 1f);
                    break;
                case ShadowPreset.Strong:
                    SetShadowParams(new Vector2(3f, -3f), 2.5f, new Color(0f, 0f, 0f, 0.7f), 1.3f);
                    break;
                case ShadowPreset.Dramatic:
                    SetShadowParams(new Vector2(5f, -5f), 4f, new Color(0f, 0f, 0f, 0.8f), 1.5f);
                    break;
            }
        }

        /// <summary>
        /// 创建带预设的阴影滤镜
        /// </summary>
        public static ShadowFilter CreateWithPreset(ShadowPreset preset)
        {
            var filter = new ShadowFilter();
            filter.ApplyPreset(preset);
            return filter;
        }
    }
}
