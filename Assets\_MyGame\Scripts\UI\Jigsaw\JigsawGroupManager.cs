using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 拼块成组逻辑管理器，负责处理拼块的自动成组功能
/// </summary>
public class JigsawGroupManager
{
    private List<JigsawGroup> groups = new List<JigsawGroup>();
    private JigsawPanel parentPanel;

    public JigsawGroupManager(JigsawPanel panel)
    {
        parentPanel = panel;
    }

    /// <summary>
    /// 获取所有组
    /// </summary>
    public List<JigsawGroup> Groups => new List<JigsawGroup>(groups);

    /// <summary>
    /// 检查并创建拼块组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    public void CheckAndCreateGroups(JigsawPiece newPiece)
    {
        if (newPiece == null) return;

        // 获取操作层中的所有拼块
        var operationPieces = parentPanel.GetPiecesInOperationLayer();

        // 如果新拼块属于某个组，需要检查整个组与其他拼块的相邻关系
        var piecesToCheck = GetPiecesToCheck(newPiece);

        // 查找与检查拼块相邻的拼块（需要同时满足原图相邻和操作区域格子相邻）
        var adjacentPieces = FindAdjacentPieces(piecesToCheck, operationPieces);

        if (adjacentPieces.Count == 0)
        {
            // 没有相邻拼块，不需要创建组
            return;
        }

        // 处理组的创建和合并
        ProcessGroupCreationAndMerging(newPiece, adjacentPieces);

        // 清理空的组
        CleanupEmptyGroups();
    }

    /// <summary>
    /// 获取需要检查的拼块列表
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <returns>需要检查的拼块列表</returns>
    private List<JigsawPiece> GetPiecesToCheck(JigsawPiece newPiece)
    {
        var piecesToCheck = new List<JigsawPiece>();
        var newPieceGroup = newPiece.GetGroup();
        
        if (newPieceGroup != null)
        {
            // 如果拼块属于组，检查组中所有拼块
            piecesToCheck.AddRange(newPieceGroup.Pieces);
        }
        else
        {
            // 如果拼块不属于组，只检查单个拼块
            piecesToCheck.Add(newPiece);
        }

        return piecesToCheck;
    }

    /// <summary>
    /// 查找与指定拼块相邻的拼块
    /// </summary>
    /// <param name="piecesToCheck">要检查的拼块列表</param>
    /// <param name="operationPieces">操作层中的所有拼块</param>
    /// <returns>相邻的拼块列表</returns>
    private List<JigsawPiece> FindAdjacentPieces(List<JigsawPiece> piecesToCheck, List<JigsawPiece> operationPieces)
    {
        var adjacentPieces = new List<JigsawPiece>();
        
        foreach (var checkPiece in piecesToCheck)
        {
            foreach (var piece in operationPieces)
            {
                if (!piecesToCheck.Contains(piece) && IsAdjacentInBothOriginalAndOperation(checkPiece, piece))
                {
                    if (!adjacentPieces.Contains(piece))
                    {
                        adjacentPieces.Add(piece);
                    }
                }
            }
        }

        return adjacentPieces;
    }

    /// <summary>
    /// 检查两个拼块是否同时满足原图相邻和操作区域格子相邻，且相对位置关系正确
    /// </summary>
    /// <param name="piece1">拼块1</param>
    /// <param name="piece2">拼块2</param>
    /// <returns>是否同时满足相邻条件和正确的相对位置关系</returns>
    private bool IsAdjacentInBothOriginalAndOperation(JigsawPiece piece1, JigsawPiece piece2)
    {
        if (piece1 == null || piece2 == null) return false;

        // 首先检查原图中是否相邻
        if (!piece1.IsAdjacentTo(piece2))
        {
            return false;
        }

        // 获取原图中的相对位置关系
        Vector2Int piece1OriginalPos = piece1.GetOriginalGridPosition();
        Vector2Int piece2OriginalPos = piece2.GetOriginalGridPosition();
        Vector2Int originalRelativePos = piece2OriginalPos - piece1OriginalPos;

        // 获取操作区域中的位置
        Vector2 piece1Center = piece1.LocalToGlobal(new Vector2(piece1.width * 0.5f, piece1.height * 0.5f));
        Vector2 piece2Center = piece2.LocalToGlobal(new Vector2(piece2.width * 0.5f, piece2.height * 0.5f));

        Vector2 piece1OperationPos = parentPanel.GlobalToOperationLayerLocal(piece1Center);
        Vector2 piece2OperationPos = parentPanel.GlobalToOperationLayerLocal(piece2Center);

        Vector2Int piece1GridPos = parentPanel.GetGridPosition(piece1OperationPos);
        Vector2Int piece2GridPos = parentPanel.GetGridPosition(piece2OperationPos);
        Vector2Int operationRelativePos = piece2GridPos - piece1GridPos;

        // 检查相对位置关系是否一致
        return originalRelativePos == operationRelativePos;
    }

    /// <summary>
    /// 处理组的创建和合并逻辑
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <param name="adjacentPieces">相邻的拼块列表</param>
    private void ProcessGroupCreationAndMerging(JigsawPiece newPiece, List<JigsawPiece> adjacentPieces)
    {
        // 检查相邻拼块是否已经属于某个组，同时包含新拼块的组（如果有的话）
        var existingGroups = CollectExistingGroups(newPiece, adjacentPieces);

        if (existingGroups.Count == 0)
        {
            CreateNewGroup(newPiece, adjacentPieces);
        }
        else if (existingGroups.Count == 1)
        {
            MergeIntoSingleGroup(newPiece, adjacentPieces, existingGroups.First());
        }
        else
        {
            MergeMultipleGroups(newPiece, adjacentPieces, existingGroups);
        }
    }

    /// <summary>
    /// 收集现有的组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <param name="adjacentPieces">相邻的拼块列表</param>
    /// <returns>现有组的集合</returns>
    private HashSet<JigsawGroup> CollectExistingGroups(JigsawPiece newPiece, List<JigsawPiece> adjacentPieces)
    {
        var existingGroups = new HashSet<JigsawGroup>();

        // 如果新拼块已经属于某个组，将该组也加入到现有组列表中
        var newPieceGroup = newPiece.GetGroup();
        if (newPieceGroup != null)
        {
            existingGroups.Add(newPieceGroup);
        }

        foreach (var piece in adjacentPieces)
        {
            var group = piece.GetGroup();
            if (group != null)
            {
                existingGroups.Add(group);
            }
        }

        return existingGroups;
    }

    /// <summary>
    /// 创建新组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <param name="adjacentPieces">相邻的拼块列表</param>
    private void CreateNewGroup(JigsawPiece newPiece, List<JigsawPiece> adjacentPieces)
    {
        var newGroup = new JigsawGroup(parentPanel);
        newGroup.AddPiece(newPiece);
        foreach (var piece in adjacentPieces)
        {
            newGroup.AddPiece(piece);
        }
        groups.Add(newGroup);
    }

    /// <summary>
    /// 合并到单个现有组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <param name="adjacentPieces">相邻的拼块列表</param>
    /// <param name="existingGroup">现有组</param>
    private void MergeIntoSingleGroup(JigsawPiece newPiece, List<JigsawPiece> adjacentPieces, JigsawGroup existingGroup)
    {
        // 如果新拼块不属于这个组，将其加入
        if (newPiece.GetGroup() != existingGroup)
        {
            existingGroup.AddPiece(newPiece);
        }

        foreach (var piece in adjacentPieces)
        {
            if (piece.GetGroup() == null)
            {
                existingGroup.AddPiece(piece);
            }
            else if (piece.GetGroup() != existingGroup)
            {
                // 如果相邻拼块属于不同的组，需要合并组
                var otherGroup = piece.GetGroup();
                if (existingGroup.CanMergeWith(otherGroup))
                {
                    existingGroup.MergeWith(otherGroup);
                    groups.Remove(otherGroup);
                    otherGroup.Dispose();
                }
            }
        }
    }

    /// <summary>
    /// 合并多个现有组
    /// </summary>
    /// <param name="newPiece">新放置的拼块</param>
    /// <param name="adjacentPieces">相邻的拼块列表</param>
    /// <param name="existingGroups">现有组的集合</param>
    private void MergeMultipleGroups(JigsawPiece newPiece, List<JigsawPiece> adjacentPieces, HashSet<JigsawGroup> existingGroups)
    {
        var primaryGroup = existingGroups.First();

        // 检查组合并的条件
        var groupsToMerge = new List<JigsawGroup>();
        foreach (var group in existingGroups.Skip(1))
        {
            if (primaryGroup.CanMergeWith(group))
            {
                groupsToMerge.Add(group);
            }
        }

        // 如果新拼块不属于主组，将其加入
        if (newPiece.GetGroup() != primaryGroup)
        {
            primaryGroup.AddPiece(newPiece);
        }

        // 合并其他组到主组
        var groupsToRemove = new List<JigsawGroup>();
        foreach (var group in groupsToMerge)
        {
            if (primaryGroup.MergeWith(group))
            {
                groupsToRemove.Add(group);
            }
        }

        // 移除已合并的组
        foreach (var group in groupsToRemove)
        {
            groups.Remove(group);
            group.Dispose();
        }

        // 添加没有组的相邻拼块
        foreach (var piece in adjacentPieces)
        {
            if (piece.GetGroup() == null)
            {
                primaryGroup.AddPiece(piece);
            }
        }
    }

    /// <summary>
    /// 清理空的组
    /// </summary>
    private void CleanupEmptyGroups()
    {
        var emptyGroups = groups.Where(g => g.Count == 0).ToList();
        foreach (var emptyGroup in emptyGroups)
        {
            groups.Remove(emptyGroup);
            emptyGroup.Dispose();
        }
    }

    /// <summary>
    /// 添加组到管理器
    /// </summary>
    /// <param name="group">要添加的组</param>
    public void AddGroup(JigsawGroup group)
    {
        if (group != null && !groups.Contains(group))
        {
            groups.Add(group);
        }
    }

    /// <summary>
    /// 从管理器中移除组
    /// </summary>
    /// <param name="group">要移除的组</param>
    public void RemoveGroup(JigsawGroup group)
    {
        if (group != null)
        {
            groups.Remove(group);
        }
    }

    /// <summary>
    /// 清理所有组
    /// </summary>
    public void ClearAllGroups()
    {
        foreach (var group in groups)
        {
            group.Dispose();
        }
        groups.Clear();
    }
}
